#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test language change functionality
"""

import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import translation system
try:
    from translations import get_translation
    from language_manager import get_language_manager
    TRANSLATIONS_AVAILABLE = True
    print("✅ Translation system loaded successfully")
except ImportError as e:
    TRANSLATIONS_AVAILABLE = False
    print(f"⚠️ Translation system not available: {e}")

def test_language_change():
    """Test language change functionality"""
    print("\n🧪 Testing language change functionality...")
    
    if not TRANSLATIONS_AVAILABLE:
        print("❌ Translation system not available")
        return
    
    # Test language manager
    try:
        manager = get_language_manager()
        
        # Test translations for each language
        test_keys = ["HOME", "BATTERY", "ENGINE", "SETTINGS", "LANGUAGE"]
        
        for language in ["English", "Polski", "<PERSON>utsch", "Русский"]:
            print(f"\n🌍 Testing language: {language}")
            
            # Set language
            success = manager.set_language(language)
            print(f"  Set language result: {success}")
            
            if success:
                # Test translations
                for key in test_keys:
                    translation = manager.get_translation(key)
                    print(f"  {key}: {translation}")
                    
                # Test direct translation function
                print(f"  Direct translation test:")
                for key in test_keys:
                    direct_translation = get_translation(key, language)
                    print(f"    {key}: {direct_translation}")
            
        print("\n✅ Language change test completed")
        
    except Exception as e:
        print(f"❌ Error testing language change: {e}")
        import traceback
        traceback.print_exc()

def test_ui_update_simulation():
    """Simulate UI update after language change"""
    print("\n🧪 Simulating UI update after language change...")
    
    # Simulate HomeScreen update_language method
    class MockHomeScreen:
        def __init__(self):
            self.ids = {
                'home_title_label': MockLabel("HOME"),
                'lightning_label': MockLabel("LIGHTNING"),
                'alarm_label': MockLabel("ALARM"),
                'climate_label': MockLabel("CLIMATE"),
                'battery_label': MockLabel("BATTERY"),
                'engine_label': MockLabel("ENGINE"),
                'water_label': MockLabel("WATER"),
                'autopilot_label': MockLabel("AUTOPILOT"),
                'fuel_label': MockLabel("FUEL")
            }
        
        def update_language(self, language=None):
            """Update all text elements with current language"""
            try:
                print(f"🔄 Updating MockHomeScreen language to: {language}")
                
                if not TRANSLATIONS_AVAILABLE:
                    print("❌ Translation system not available")
                    return
                
                # Update all labels with translations using IDs
                label_mappings = {
                    'home_title_label': 'HOME',
                    'lightning_label': 'LIGHTNING', 
                    'alarm_label': 'ALARM',
                    'climate_label': 'CLIMATE',
                    'battery_label': 'BATTERY',
                    'engine_label': 'ENGINE',
                    'water_label': 'WATER',
                    'autopilot_label': 'AUTOPILOT',
                    'fuel_label': 'FUEL'
                }
                
                for label_id, translation_key in label_mappings.items():
                    try:
                        label = self.ids.get(label_id)
                        if label:
                            new_text = get_translation(translation_key, language)
                            label.text = new_text
                            print(f"  📝 Updated {label_id}: {translation_key} → {new_text}")
                        else:
                            print(f"  ⚠️ Label {label_id} not found")
                    except Exception as e:
                        print(f"  ❌ Error updating {label_id}: {e}")
                
                print(f"✅ MockHomeScreen language updated to: {language}")
                
            except Exception as e:
                print(f"❌ Error updating MockHomeScreen language: {e}")
                import traceback
                traceback.print_exc()
    
    class MockLabel:
        def __init__(self, text):
            self.text = text
    
    # Test UI update simulation
    mock_screen = MockHomeScreen()
    
    for language in ["English", "Polski", "Deutsch", "Русский"]:
        print(f"\n🔄 Simulating UI update for: {language}")
        mock_screen.update_language(language)

if __name__ == "__main__":
    print("🧪 Starting Language Change Test")
    print("=" * 50)
    
    test_language_change()
    test_ui_update_simulation()
    
    print("\n" + "=" * 50)
    print("🏁 Language change test finished")

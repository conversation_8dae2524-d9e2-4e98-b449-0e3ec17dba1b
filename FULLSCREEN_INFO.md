# TridentOS - <PERSON><PERSON><PERSON>al<PERSON><PERSON>ć Fullscreen

## NATYCHMIASTOWY FULLSCREEN NA WSZYSTKICH PLATFORMACH

TridentOS uruchamia się w trybie fullscreen NATYCHMIAST na wszystkich platformach:

### 🖥️ **Raspberry Pi 5**
- **NATYCHMIASTOWY fullscreen** od pierwszej klatki
- **Bez ramki okna** dla maksymalnego wykorzystania ekranu
- **Auto-detect rozdzielczości** ekranu
- **Kursor wido<PERSON>ny** dla obsługi touchscreen
- **Czarne tło** dla oszczędności energii
- **Stały fullscreen** - brak możliwości wyjścia

### 💻 **PC/Windows**
- **NATYCHMIASTOWY fullscreen** od pierwszej klatki
- **Auto-detect rozdzielczości** ekranu
- **Bez ramki okna** dla pełnego ekranu
- **ESC** - wyj<PERSON><PERSON> z fullscreen do okna
- **F11** - powr<PERSON>t do fullscreen
- **<PERSON><PERSON><PERSON> tło** dla lepszego wyglądu

## Sterowanie fullscreen (PC)

### Klawisze skrótów:
- **ESC** - Wyjdź z fullscreen do trybu okna (1280x720)
- **F11** - Powróć do fullscreen z trybu okna

### Tryby wyświetlania:
1. **Fullscreen** - DOMYŚLNY tryb, pełny ekran bez ramki (auto-rozdzielczość)
2. **Tryb okna** - dostępny po naciśnięciu ESC (1280x720)

## Konfiguracja techniczna

### Ustawienia Kivy Config (automatyczne):

**Raspberry Pi 5:**
```ini
fullscreen = auto
borderless = 1
window_state = maximized
width = 0          # Auto-detect
height = 0         # Auto-detect
show_cursor = 1
```

**PC/Windows:**
```ini
fullscreen = auto  # FULLSCREEN DOMYŚLNIE
borderless = 1     # Bez ramki
width = 0          # Auto-detect
height = 0         # Auto-detect
show_cursor = 1
```

### Zmienne środowiskowe (RPi5):
```bash
KIVY_GL_BACKEND=gl
KIVY_WINDOW=sdl2
KIVY_METRICS_DENSITY=1
KIVY_METRICS_FONTSCALE=1
```

## Implementacja

### Automatyczna konfiguracja:
1. **Wykrywanie platformy** przy starcie
2. **Ustawienie Config** przed inicjalizacją Kivy
3. **Programowa konfiguracja** po zbudowaniu aplikacji
4. **Obsługa zdarzeń** klawiatury (PC)

### Kod kluczowy:
```python
def setup_fullscreen(self, dt):
    from kivy.core.window import Window
    
    if is_raspberry_pi():
        # RPi5 - automatyczny fullscreen
        Window.fullscreen = 'auto'
        Window.borderless = True
        Window.show_cursor = True
    else:
        # PC - tryb okna z obsługą F11
        Window.fullscreen = False
        Window.borderless = False
        Window.bind(on_key_down=self.on_key_down)
```

## Dostosowanie

### Wyłączenie automatycznego fullscreen na RPi5:
Edytuj `config/rpi5_config.py`:
```python
'fullscreen': False,
'borderless': False,
```

### Wymuszenie fullscreen na PC:
Edytuj `config/rpi5_config.py` w sekcji `else`:
```python
'fullscreen': 'auto',
'borderless': True,
```

### Wyłączenie obsługi klawiszy F11/ESC:
W `main.py` usuń linię:
```python
Window.bind(on_key_down=self.on_key_down)
```

## Rozwiązywanie problemów

### Problem: Fullscreen nie działa na RPi5
1. Sprawdź konfigurację GPU w `/boot/config.txt`
2. Sprawdź czy X11 jest poprawnie skonfigurowany
3. Sprawdź logi Kivy: `~/.kivy/logs/`

### Problem: Nie można wyjść z fullscreen na PC
1. Naciśnij **ESC** lub **F11**
2. Użyj **Alt+Tab** do przełączenia okien
3. Użyj **Ctrl+Alt+Del** w ostateczności

### Problem: Kursor nie jest widoczny
1. Na RPi5: Sprawdź konfigurację touchscreen
2. Na PC: Sprawdź sterowniki myszy
3. W kodzie: `Window.show_cursor = True`

## Zalety implementacji

### ✅ **Automatyzacja**
- Brak konieczności ręcznej konfiguracji
- Automatyczne dostosowanie do platformy
- Inteligentne wykrywanie sprzętu

### ✅ **Elastyczność**
- Łatwe przełączanie na PC
- Możliwość dostosowania konfiguracji
- Zachowana kompatybilność

### ✅ **Optymalizacja**
- Maksymalne wykorzystanie ekranu na RPi5
- Oszczędność energii (czarne tło)
- Zoptymalizowane dla touchscreen

### ✅ **Użyteczność**
- Intuicyjne sterowanie (F11/ESC)
- Widoczny kursor dla interakcji
- Profesjonalny wygląd aplikacji

## Testowanie

### Test na PC:
1. Uruchom aplikację - powinna wystartować w oknie
2. Naciśnij F11 - przełączenie na fullscreen
3. Naciśnij ESC - powrót do okna
4. Sprawdź tytuł okna

### Test na RPi5:
1. Uruchom aplikację - powinna wystartować w fullscreen
2. Sprawdź czy kursor jest widoczny
3. Sprawdź czy aplikacja zajmuje cały ekran
4. Sprawdź responsywność touchscreen

## Changelog

### v1.0 - Podstawowa implementacja
- ✅ Automatyczne wykrywanie platformy
- ✅ Konfiguracja fullscreen dla RPi5
- ✅ Tryb okna dla PC

### v1.1 - Obsługa klawiszy
- ✅ F11 - przełączanie fullscreen (PC)
- ✅ ESC - wyjście z fullscreen (PC)
- ✅ Obsługa zdarzeń klawiatury

### v1.2 - Optymalizacje
- ✅ Czarne tło dla oszczędności energii
- ✅ Konfiguracja w rpi5_config.py
- ✅ Dokumentacja i instrukcje

Funkcjonalność fullscreen jest teraz w pełni zintegrowana z systemem optymalizacji TridentOS!

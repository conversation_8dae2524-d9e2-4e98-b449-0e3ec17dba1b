# Instrukcje Konfiguracji Systemu Buzzer na Raspberry Pi 5

## Problem: Raspberry Pi 5 nie jest wykrywany

Jeśli TridentOS nie wykrywa Raspberry Pi 5 lub GPIO, wykonaj poniższe kroki:

## 🔧 Szybkie Rozwiązanie

### Krok 1: Uruchom Automatyczny Skrypt Naprawczy
```bash
# Przejdź do katalogu TridentOS
cd /path/to/TridentOS

# Uruchom skrypt naprawczy
./fix_gpio_rpi5.sh
```

### Krok 2: Sprawdź Wykrywanie
```bash
# Test szybkiego wykrywania
cd venv/TridentUI2
python quick_rpi_test.py

# Pełna diagnostyka
python diagnose_rpi.py
```

### Krok 3: Test Systemu Buzzer
```bash
# Test kompletnego systemu
python test_buzzer.py
```

## 🔍 Diagnostyka Manualna

### Sprawdź Informacje o Systemie
```bash
# Sprawdź model Raspberry Pi
cat /proc/cpuinfo | grep "Model"

# Sprawdź device tree
cat /proc/device-tree/model

# Sprawdź architekturę
uname -m

# Sprawdź hostname
hostname
```

### Sprawdź Dostępność GPIO
```bash
# Sprawdź urządzenia GPIO
ls -la /dev/gpio*
ls -la /sys/class/gpio/

# Sprawdź grupy użytkownika
groups

# Sprawdź uprawnienia
ls -la /dev/gpiomem
```

## 🛠️ Instalacja Manualna

### 1. Zainstaluj Biblioteki GPIO
```bash
# Aktywuj środowisko wirtualne
source venv_rpi5/bin/activate

# Zainstaluj biblioteki
pip install --upgrade gpiozero
pip install --upgrade pigpio
pip install --upgrade RPi.GPIO

# Sprawdź instalację
python -c "import gpiozero; print(f'gpiozero {gpiozero.__version__}')"
python -c "import RPi.GPIO; print(f'RPi.GPIO {RPi.GPIO.VERSION}')"
python -c "import pigpio; print('pigpio OK')"
```

### 2. Skonfiguruj pigpio Daemon
```bash
# Włącz usługę pigpio
sudo systemctl enable pigpiod

# Uruchom usługę
sudo systemctl start pigpiod

# Sprawdź status
sudo systemctl status pigpiod
```

### 3. Skonfiguruj Uprawnienia GPIO
```bash
# Dodaj użytkownika do grupy gpio
sudo usermod -a -G gpio $USER

# Utwórz reguły udev
sudo tee /etc/udev/rules.d/99-gpio.rules > /dev/null << 'EOF'
# GPIO permissions for TridentOS
SUBSYSTEM=="gpio", GROUP="gpio", MODE="0664"
KERNEL=="gpiomem", GROUP="gpio", MODE="0664"
SUBSYSTEM=="gpio", KERNEL=="gpiochip*", ACTION=="add", RUN+="/bin/chown root:gpio /sys/class/gpio/export /sys/class/gpio/unexport"
SUBSYSTEM=="gpio", KERNEL=="gpio*", ACTION=="add", RUN+="/bin/chown root:gpio /sys%p/active_low /sys%p/direction /sys%p/edge /sys%p/value", RUN+="/bin/chmod 664 /sys%p/active_low /sys%p/direction /sys%p/edge /sys%p/value"
EOF

# Przeładuj reguły udev
sudo udevadm control --reload-rules
sudo udevadm trigger

# WAŻNE: Wyloguj się i zaloguj ponownie (lub zrestartuj)
```

## 🔌 Podłączenie Sprzętu

### Schemat Połączeń
```
Raspberry Pi 5          Buzzer
GPIO PIN 5 (BCM) -----> Positive (+)
GND              -----> Negative (-)
```

### Lokalizacja Pinów na Raspberry Pi 5
```
     3V3  (1) (2)  5V
   GPIO2  (3) (4)  5V
   GPIO3  (5) (6)  GND
   GPIO4  (7) (8)  GPIO14
     GND  (9) (10) GPIO15
  GPIO17 (11) (12) GPIO18
  GPIO27 (13) (14) GND
  GPIO22 (15) (16) GPIO23
     3V3 (17) (18) GPIO24
  GPIO10 (19) (20) GND
   GPIO9 (21) (22) GPIO25
  GPIO11 (23) (24) GPIO8
     GND (25) (26) GPIO7
   GPIO0 (27) (28) GPIO1
   GPIO5 (29) (30) GND  <-- Użyj PIN 29 (GPIO5) i PIN 30 (GND)
   GPIO6 (31) (32) GPIO12
  GPIO13 (33) (34) GND
  GPIO19 (35) (36) GPIO16
  GPIO26 (37) (38) GPIO20
     GND (39) (40) GPIO21
```

## 🧪 Testowanie

### Test Podstawowy
```bash
# Test wykrywania Raspberry Pi
cd venv/TridentUI2
python -c "
from buzzer_controller import detect_raspberry_pi, IS_RASPBERRY_PI
print(f'Raspberry Pi detected: {detect_raspberry_pi()}')
print(f'Global flag: {IS_RASPBERRY_PI}')
"
```

### Test GPIO
```bash
# Test manualny GPIO PIN 5
echo "5" > /sys/class/gpio/export
echo "out" > /sys/class/gpio/gpio5/direction
echo "1" > /sys/class/gpio/gpio5/value  # Buzzer ON
sleep 1
echo "0" > /sys/class/gpio/gpio5/value  # Buzzer OFF
echo "5" > /sys/class/gpio/unexport
```

### Test Kompletnego Systemu
```bash
# Test systemu buzzer
cd venv/TridentUI2
python test_buzzer.py

# Test w aplikacji TridentOS
cd ../..
./start_tridentos_rpi5.sh
# Następnie w aplikacji:
# 1. Przejdź do ekranu Alarms
# 2. Naciśnij "Add Test Alarm"
# 3. Sprawdź czy buzzer się włącza
# 4. Naciśnij "CONFIRM ALARM"
# 5. Sprawdź czy buzzer się wyłącza
```

## ⚠️ Rozwiązywanie Problemów

### Problem: "Raspberry Pi not detected"
**Rozwiązanie:**
1. Sprawdź `/proc/cpuinfo` i `/proc/device-tree/model`
2. Jeśli to rzeczywiście Raspberry Pi, edytuj `buzzer_controller.py`:
   ```python
   IS_RASPBERRY_PI = True  # Wymuś wykrywanie
   ```

### Problem: "GPIO libraries not available"
**Rozwiązanie:**
```bash
source venv_rpi5/bin/activate
pip install gpiozero pigpio RPi.GPIO
```

### Problem: "Permission denied" na GPIO
**Rozwiązanie:**
```bash
sudo usermod -a -G gpio $USER
# Następnie wyloguj się i zaloguj ponownie
```

### Problem: "pigpio daemon not running"
**Rozwiązanie:**
```bash
sudo systemctl start pigpiod
sudo systemctl enable pigpiod
```

### Problem: Buzzer nie działa mimo braku błędów
**Rozwiązanie:**
1. Sprawdź połączenia sprzętowe
2. Sprawdź czy buzzer jest aktywny (nie pasywny)
3. Sprawdź napięcie (3.3V vs 5V)
4. Dodaj rezystor 220Ω jeśli potrzebny

## 📞 Wsparcie

Jeśli problemy nadal występują:

1. **Uruchom pełną diagnostykę:**
   ```bash
   cd venv/TridentUI2
   python diagnose_rpi.py > diagnostics.log 2>&1
   ```

2. **Sprawdź logi systemu:**
   ```bash
   journalctl -u pigpiod
   dmesg | grep gpio
   ```

3. **Wyślij informacje diagnostyczne** wraz z:
   - Model Raspberry Pi (`cat /proc/cpuinfo | grep Model`)
   - Wersja systemu (`cat /etc/os-release`)
   - Wynik `python diagnose_rpi.py`

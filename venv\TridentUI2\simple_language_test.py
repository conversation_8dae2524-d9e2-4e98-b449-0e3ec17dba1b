#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple test for language buttons functionality
"""

import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import Config przed jego użyciem
from kivy.config import Config

# Podstawowe ustawienia
Config.set('graphics', 'width', '800')
Config.set('graphics', 'height', '600')

from kivy.app import App
from kivy.lang import Builder
from kivy.uix.screenmanager import ScreenManager, Screen
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.button import Button
from kivy.uix.label import Label
from kivy.properties import StringProperty

# Import translation system
try:
    from translations import get_translation, get_available_languages
    from language_manager import get_language_manager, translate
    TRANSLATIONS_AVAILABLE = True
    print("✅ Translation system loaded successfully")
except ImportError as e:
    TRANSLATIONS_AVAILABLE = False
    print(f"⚠️ Translation system not available: {e}")

    def get_translation(key, language="English"):
        return key

    def get_available_languages():
        return ["English", "<PERSON>ski", "Deutsch", "Русский"]

# Simple KV string for testing language buttons
KV_STRING = '''
<SimpleLanguageScreen>:
    BoxLayout:
        orientation: 'vertical'
        padding: 20
        spacing: 20
        
        Label:
            text: 'Simple Language Button Test'
            font_size: '24sp'
            size_hint_y: 0.2
            
        Label:
            text: f'Current Language: {root.current_language}'
            font_size: '18sp'
            size_hint_y: 0.1
            
        Label:
            text: f'Translation Test - HOME: {root.get_translation("HOME")}'
            font_size: '16sp'
            size_hint_y: 0.1
            
        BoxLayout:
            orientation: 'horizontal'
            spacing: 10
            size_hint_y: 0.3
            
            Button:
                text: 'English'
                font_size: '16sp'
                background_color: (0.2, 0.4, 0.6, 1) if root.current_language == 'English' else (0.3, 0.3, 0.3, 1)
                on_press: root.test_language_button('English')
                
            Button:
                text: 'Polski'
                font_size: '16sp'
                background_color: (0.2, 0.4, 0.6, 1) if root.current_language == 'Polski' else (0.3, 0.3, 0.3, 1)
                on_press: root.test_language_button('Polski')
                
            Button:
                text: 'Deutsch'
                font_size: '16sp'
                background_color: (0.2, 0.4, 0.6, 1) if root.current_language == 'Deutsch' else (0.3, 0.3, 0.3, 1)
                on_press: root.test_language_button('Deutsch')
                
            Button:
                text: 'Russian'
                font_size: '16sp'
                background_color: (0.2, 0.4, 0.6, 1) if root.current_language == 'Русский' else (0.3, 0.3, 0.3, 1)
                on_press: root.test_language_button('Русский')
        
        BoxLayout:
            orientation: 'vertical'
            spacing: 10
            size_hint_y: 0.3
            
            Label:
                text: 'Console Output:'
                font_size: '16sp'
                size_hint_y: 0.2
                
            Label:
                text: root.console_output
                font_size: '14sp'
                text_size: self.size
                halign: 'left'
                valign: 'top'
                size_hint_y: 0.8
'''

class SimpleLanguageScreen(Screen):
    current_language = StringProperty("English")
    console_output = StringProperty("Ready to test language buttons...")
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        print("🧪 SimpleLanguageScreen initialized")
        
        # Initialize with current language from language manager if available
        if TRANSLATIONS_AVAILABLE:
            try:
                manager = get_language_manager()
                self.current_language = manager.get_current_language()
                print(f"✅ Loaded current language: {self.current_language}")
                self.console_output = f"Language manager loaded. Current: {self.current_language}"
            except Exception as e:
                print(f"⚠️ Could not load current language: {e}")
                self.console_output = f"Error loading language manager: {e}"
        else:
            self.console_output = "Translation system not available"
    
    def test_language_button(self, language):
        """Test language button functionality"""
        print(f"\n🧪 LANGUAGE BUTTON CLICKED: {language}")
        print(f"🔍 Current language before: {self.current_language}")
        
        output_lines = [
            f"Button clicked: {language}",
            f"Before: {self.current_language}"
        ]
        
        try:
            if TRANSLATIONS_AVAILABLE:
                print(f"🔧 Using language manager...")
                manager = get_language_manager()
                success = manager.set_language(language)
                
                if success:
                    old_language = self.current_language
                    self.current_language = language
                    print(f"✅ Language changed successfully: {old_language} → {language}")
                    
                    # Force UI update
                    self.property('current_language').dispatch(self)
                    
                    # Test translation
                    home_translation = manager.get_translation('HOME')
                    settings_translation = manager.get_translation('SETTINGS')
                    
                    output_lines.extend([
                        f"✅ Success: {old_language} → {language}",
                        f"HOME: {home_translation}",
                        f"SETTINGS: {settings_translation}"
                    ])
                    
                    print(f"🔍 Translation test:")
                    print(f"  HOME: {home_translation}")
                    print(f"  SETTINGS: {settings_translation}")
                    
                else:
                    output_lines.append(f"❌ Failed to change to {language}")
                    print(f"❌ Failed to change language to: {language}")
            else:
                print(f"⚠️ Translation system not available, setting language directly")
                old_language = self.current_language
                self.current_language = language
                self.property('current_language').dispatch(self)
                output_lines.extend([
                    f"⚠️ Direct change: {old_language} → {language}",
                    "Translation system not available"
                ])
                
        except Exception as e:
            error_msg = f"❌ Error: {e}"
            output_lines.append(error_msg)
            print(f"❌ Error in test_language_button: {e}")
            import traceback
            traceback.print_exc()
        
        # Update console output
        self.console_output = "\n".join(output_lines[-6:])  # Show last 6 lines
        
        print(f"🔍 Current language after: {self.current_language}")
        print(f"✅ Test completed\n")
    
    def get_translation(self, key):
        """Get translation for current language"""
        try:
            if TRANSLATIONS_AVAILABLE:
                manager = get_language_manager()
                return manager.get_translation(key)
            else:
                return key
        except Exception as e:
            print(f"❌ Error getting translation for {key}: {e}")
            return key

class SimpleLanguageApp(App):
    def build(self):
        print("🚀 Building SimpleLanguageApp...")
        
        # Load KV string
        Builder.load_string(KV_STRING)
        
        # Create screen manager
        sm = ScreenManager()
        
        # Add test screen
        test_screen = SimpleLanguageScreen(name='test')
        sm.add_widget(test_screen)
        
        print("✅ SimpleLanguageApp built successfully")
        return sm

def main():
    """Main test function"""
    print("🧪 Starting Simple Language Button Test")
    print("=" * 50)
    
    if TRANSLATIONS_AVAILABLE:
        print("✅ Translation system available")
        
        # Test language manager
        try:
            manager = get_language_manager()
            languages = manager.get_supported_languages()
            print(f"📋 Supported languages:")
            for lang in languages:
                status = "✅ CURRENT" if lang['is_current'] else "  "
                print(f"  {status} {lang['flag']} {lang['name']} - {lang['coverage']:.1f}%")
        except Exception as e:
            print(f"❌ Error testing language manager: {e}")
    else:
        print("⚠️ Translation system not available")
    
    print("\n🎮 Starting GUI test...")
    print("Click the language buttons to test functionality")
    print("Check console output and GUI for results")
    
    # Run the app
    SimpleLanguageApp().run()

if __name__ == "__main__":
    main()

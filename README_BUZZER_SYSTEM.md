# TridentOS Critical Alarm Buzzer System

## Overview

The TridentOS marine control system now includes a comprehensive critical alarm buzzer system designed to ensure that critical alarms cannot be missed. This system follows maritime safety standards and provides audible alerts for emergency and critical conditions.

## Features

### 🔔 Hardware Integration
- **GPIO PIN 5** buzzer control on Raspberry Pi 5
- Support for both **GPIO Zero** and **RPi.GPIO** libraries
- **PiGPIO** factory for enhanced performance
- Automatic fallback between GPIO libraries

### 🚨 Alarm Integration
- Automatic buzzer activation for **EMERGENCY** and **CRITICAL** alarms
- Integration with existing maritime alarm standards (IMO MSC.302(87))
- Continuous beeping pattern: **500ms ON, 500ms OFF**
- Background thread operation (non-blocking)

### 🛡️ Safety Features
- **Debounce protection** (minimum 1 second between activations)
- **Maximum duration limit** (5 minutes auto-deactivation)
- **Error handling** for GPIO failures
- **Graceful degradation** on non-Raspberry Pi systems

### 🎛️ Control Interface
- **"CONFIRM ALARM" button** in alarm UI
- **Buzzer status indicator** showing active/silent state
- **Emergency override** functionality
- **Automatic deactivation** when alarms are acknowledged or resolved

## Hardware Setup

### Required Components
- Raspberry Pi 5
- Active buzzer (5V or 3.3V compatible)
- Connecting wires
- Optional: Resistor (220Ω) for current limiting

### Wiring Diagram
```
Raspberry Pi 5          Buzzer
GPIO PIN 5 (BCM) -----> Positive (+)
GND              -----> Negative (-)
```

### GPIO Pin Configuration
- **PIN 5** (BCM numbering) - Buzzer control
- **GND** - Ground connection

## Software Installation

### Automatic Installation (Recommended)
```bash
# Run the updated installation script
./install_rpi5.sh
```

The installation script now automatically installs:
- `gpiozero` - Primary GPIO library
- `pigpio` - High-performance GPIO daemon
- `RPi.GPIO` - Fallback GPIO library

### Manual Installation
```bash
# Activate virtual environment
source venv_rpi5/bin/activate

# Install GPIO libraries
pip install gpiozero pigpio RPi.GPIO

# Configure pigpio daemon
sudo systemctl enable pigpiod
sudo systemctl start pigpiod

# Add user to GPIO group
sudo usermod -a -G gpio $USER
```

## Usage

### Automatic Operation
The buzzer system operates automatically:

1. **Alarm Triggered**: When a critical or emergency alarm occurs
2. **Buzzer Activates**: Continuous beeping starts (500ms ON/OFF)
3. **User Response**: Press "CONFIRM ALARM" button in alarm UI
4. **Buzzer Stops**: Automatic deactivation when alarms acknowledged

### Manual Control
```python
# Get buzzer controller
from buzzer_controller import get_buzzer_controller
buzzer = get_buzzer_controller()

# Activate buzzer
buzzer.activate("critical", "Manual test")

# Check status
is_active = buzzer.is_buzzer_active()

# Deactivate buzzer
buzzer.deactivate("Manual stop")

# Get statistics
stats = buzzer.get_stats()
```

### Alarm Manager Integration
```python
# Get alarm manager
import alarm_manager
alarm_mgr = alarm_manager.get_instance()

# Add critical alarm (automatically activates buzzer)
alarm_id = alarm_mgr.add_alarm(
    alarm_manager.ALARM_CRITICAL,
    "Critical system failure",
    alarm_manager.SOURCE_SYSTEM
)

# Acknowledge all critical alarms (deactivates buzzer)
acknowledged_count = alarm_mgr.acknowledge_all_critical_alarms("Operator")

# Force buzzer deactivation (emergency override)
alarm_mgr.force_buzzer_deactivation("Emergency Override")
```

## Testing

### Comprehensive Test Script
```bash
# Run the buzzer system test
cd venv/TridentUI2
python test_buzzer.py
```

The test script verifies:
- GPIO library availability
- Buzzer controller functionality
- Alarm system integration
- Hardware connectivity

### Manual Testing
1. **Start TridentOS**: `./start_tridentos_rpi5.sh`
2. **Navigate to Alarm Screen**
3. **Press "Add Test Alarm"** button
4. **Verify buzzer activation**
5. **Press "CONFIRM ALARM"** button
6. **Verify buzzer deactivation**

## Configuration

### Buzzer Parameters
Edit `venv/TridentUI2/buzzer_controller.py`:
```python
# Default configuration
pin = 5                    # GPIO pin number
beep_on_duration = 0.5     # Beep ON time (seconds)
beep_off_duration = 0.5    # Beep OFF time (seconds)
max_continuous_duration = 300  # Max runtime (seconds)
debounce_interval = 1.0    # Min time between activations
```

### Alarm Types That Trigger Buzzer
- `ALARM_EMERGENCY` - Immediate action required
- `ALARM_CRITICAL` - Critical attention required

### Alarm Types That Don't Trigger Buzzer
- `ALARM_WARNING` - Warning conditions
- `ALARM_CAUTION` - Caution conditions  
- `ALARM_INFO` - Informational messages

## Troubleshooting

### Quick Fix Script (Raspberry Pi 5)
```bash
# Run the automated fix script
./fix_gpio_rpi5.sh
```

### Quick Detection Test
```bash
# Test Raspberry Pi detection
cd venv/TridentUI2
python quick_rpi_test.py
```

### Common Issues

#### Raspberry Pi Not Detected
1. **Run Diagnostics**
   ```bash
   cd venv/TridentUI2
   python diagnose_rpi.py
   ```

2. **Check Platform Info**
   ```bash
   cat /proc/cpuinfo | grep "Model"
   cat /proc/device-tree/model
   ```

3. **Manual Detection Override** (if needed)
   Edit `buzzer_controller.py` and force:
   ```python
   IS_RASPBERRY_PI = True  # Force enable for testing
   ```

#### Buzzer Not Working
1. **Check Hardware Connection**
   ```bash
   # Test GPIO pin manually
   echo "5" > /sys/class/gpio/export
   echo "out" > /sys/class/gpio/gpio5/direction
   echo "1" > /sys/class/gpio/gpio5/value
   echo "0" > /sys/class/gpio/gpio5/value
   echo "5" > /sys/class/gpio/unexport
   ```

2. **Check GPIO Permissions**
   ```bash
   # Verify user is in gpio group
   groups $USER
   
   # Add to gpio group if missing
   sudo usermod -a -G gpio $USER
   ```

3. **Check GPIO Libraries**
   ```bash
   # Test GPIO Zero
   python -c "import gpiozero; print('GPIO Zero OK')"
   
   # Test RPi.GPIO
   python -c "import RPi.GPIO; print('RPi.GPIO OK')"
   ```

#### Pigpio Daemon Issues
```bash
# Check pigpio daemon status
sudo systemctl status pigpiod

# Start pigpio daemon
sudo systemctl start pigpiod

# Enable pigpio daemon
sudo systemctl enable pigpiod
```

#### GPIO Libraries Not Available
1. **Install Missing Libraries**
   ```bash
   # Activate virtual environment
   source venv_rpi5/bin/activate

   # Install all GPIO libraries
   pip install gpiozero pigpio RPi.GPIO
   ```

2. **Configure pigpio daemon**
   ```bash
   # Enable and start pigpio daemon
   sudo systemctl enable pigpiod
   sudo systemctl start pigpiod

   # Check status
   sudo systemctl status pigpiod
   ```

#### Permission Errors
```bash
# Add user to gpio group
sudo usermod -a -G gpio $USER

# Fix GPIO permissions
sudo chown root:gpio /dev/gpiomem
sudo chmod g+rw /dev/gpiomem

# Create udev rules
sudo tee /etc/udev/rules.d/99-gpio.rules > /dev/null << 'EOF'
SUBSYSTEM=="gpio", GROUP="gpio", MODE="0664"
KERNEL=="gpiomem", GROUP="gpio", MODE="0664"
EOF

# Reload udev rules
sudo udevadm control --reload-rules
sudo udevadm trigger

# Logout and login again (or reboot)
```

### Debug Mode
Enable debug logging in `buzzer_controller.py`:
```python
logging.basicConfig(level=logging.DEBUG)
```

## Performance Considerations

### Raspberry Pi 5 Optimizations
- Uses **PiGPIO factory** for better performance
- **Background thread** operation to avoid UI blocking
- **Efficient sleep cycles** to minimize CPU usage
- **Memory-optimized** alarm checking

### Resource Usage
- **CPU**: Minimal impact (~0.1% when active)
- **Memory**: ~1MB additional usage
- **GPIO**: Single pin (PIN 5) reservation

## Safety and Compliance

### Maritime Standards
- Follows **IMO MSC.302(87)** Bridge Alert Management
- **Category A alarms** (Emergency/Critical) trigger buzzer
- **Proper escalation** and acknowledgment procedures
- **Fail-safe operation** with automatic timeouts

### Safety Features
- **Maximum duration limit** prevents indefinite operation
- **Debounce protection** prevents rapid triggering
- **Error recovery** with graceful degradation
- **Emergency override** capability

## Integration with TridentOS

### Files Modified/Added
- `venv/TridentUI2/buzzer_controller.py` - New buzzer control module
- `venv/TridentUI2/alarm_manager.py` - Extended with buzzer integration
- `venv/TridentUI2/main.py` - Added buzzer initialization and control
- `venv/TridentUI2/ui/alarm.kv` - Added "CONFIRM ALARM" button
- `install_rpi5.sh` - Added GPIO library installation
- `venv/TridentUI2/test_buzzer.py` - Comprehensive test script

### Backward Compatibility
- **Graceful degradation** on systems without GPIO
- **No impact** on existing alarm functionality
- **Optional feature** that enhances rather than replaces existing systems

## Future Enhancements

### Planned Features
- **Multiple buzzer patterns** for different alarm types
- **Volume control** via PWM
- **Remote buzzer control** via network
- **Buzzer health monitoring**
- **Custom alarm sounds** via audio system integration

### Hardware Expansion
- **Multiple GPIO pins** for different alarm types
- **LED indicators** synchronized with buzzer
- **External siren** support for larger vessels
- **Wireless buzzer** integration

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple test for translation system
"""

import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import translation system
try:
    from translations import get_translation, get_available_languages
    from language_manager import get_language_manager, translate
    TRANSLATIONS_AVAILABLE = True
    print("✅ Translation system loaded successfully")
except ImportError as e:
    TRANSLATIONS_AVAILABLE = False
    print(f"⚠️ Translation system not available: {e}")

def test_translations():
    """Test translation system"""
    print("\n🧪 Testing translation system...")
    
    if not TRANSLATIONS_AVAILABLE:
        print("❌ Translation system not available")
        return
    
    # Test available languages
    languages = get_available_languages()
    print(f"📋 Available languages: {languages}")
    
    # Test language manager
    try:
        manager = get_language_manager()
        current = manager.get_current_language()
        print(f"🔍 Current language: {current}")
        
        # Test translations for each language
        test_keys = ["HOME", "BATTERY", "ENGINE", "SETTINGS", "LANGUAGE"]
        
        for language in ["English", "Polski", "Deutsch", "Русский"]:
            print(f"\n🌍 Testing language: {language}")
            
            # Set language
            success = manager.set_language(language)
            print(f"  Set language result: {success}")
            
            if success:
                # Test translations
                for key in test_keys:
                    translation = manager.get_translation(key)
                    print(f"  {key}: {translation}")
            
        print("\n✅ Translation test completed")
        
    except Exception as e:
        print(f"❌ Error testing translations: {e}")
        import traceback
        traceback.print_exc()

def test_direct_translations():
    """Test direct translation function"""
    print("\n🧪 Testing direct translations...")
    
    test_keys = ["HOME", "BATTERY", "ENGINE", "SETTINGS", "LANGUAGE"]
    
    for language in ["English", "Polski", "Deutsch", "Русский"]:
        print(f"\n🌍 Language: {language}")
        for key in test_keys:
            if TRANSLATIONS_AVAILABLE:
                translation = get_translation(key, language)
            else:
                translation = key
            print(f"  {key}: {translation}")

if __name__ == "__main__":
    print("🧪 Starting Translation System Test")
    print("=" * 50)
    
    test_translations()
    test_direct_translations()
    
    print("\n" + "=" * 50)
    print("🏁 Translation test finished")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test language UI updates
"""

import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import Kivy
from kivy.app import App
from kivy.lang import Builder
from kivy.uix.screenmanager import ScreenManager, Screen
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.uix.boxlayout import BoxLayout
from kivy.properties import StringProperty

# Import translation system
try:
    from translations import get_translation
    from language_manager import get_language_manager
    TRANSLATIONS_AVAILABLE = True
    print("✅ Translation system loaded successfully")
except ImportError as e:
    TRANSLATIONS_AVAILABLE = False
    print(f"⚠️ Translation system not available: {e}")

# Simple KV string for testing
KV = '''
<TestScreen>:
    BoxLayout:
        orientation: 'vertical'
        padding: 20
        spacing: 20
        
        Label:
            id: title_label
            text: 'HOME'
            font_size: '24sp'
            size_hint_y: None
            height: 50
            
        Label:
            id: battery_label
            text: 'BATTERY'
            font_size: '20sp'
            size_hint_y: None
            height: 40
            
        Label:
            id: engine_label
            text: 'ENGINE'
            font_size: '20sp'
            size_hint_y: None
            height: 40
            
        BoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: 50
            spacing: 10
            
            Button:
                text: 'English'
                on_press: app.change_language('English')
                
            Button:
                text: 'Polski'
                on_press: app.change_language('Polski')
                
            Button:
                text: 'Deutsch'
                on_press: app.change_language('Deutsch')
                
            Button:
                text: 'Русский'
                on_press: app.change_language('Русский')
'''

class TestScreen(Screen):
    def update_language(self, language=None):
        """Update all text elements with current language"""
        try:
            print(f"🔄 Updating TestScreen language...")
            
            app = App.get_running_app()
            if not app:
                print("❌ No app instance found")
                return
            
            # Get current language
            if language is None:
                language = app.get_current_language()
            
            print(f"🌍 TestScreen updating to language: {language}")
            
            # Update all labels with translations using IDs
            label_mappings = {
                'title_label': 'HOME',
                'battery_label': 'BATTERY',
                'engine_label': 'ENGINE'
            }
            
            for label_id, translation_key in label_mappings.items():
                try:
                    label = self.ids.get(label_id)
                    if label:
                        new_text = app.get_translation(translation_key, language)
                        label.text = new_text
                        print(f"  📝 Updated {label_id}: {translation_key} → {new_text}")
                    else:
                        print(f"  ⚠️ Label {label_id} not found")
                except Exception as e:
                    print(f"  ❌ Error updating {label_id}: {e}")
            
            print(f"✅ TestScreen language updated to: {language}")
            
        except Exception as e:
            print(f"❌ Error updating TestScreen language: {e}")
            import traceback
            traceback.print_exc()

class TestLanguageApp(App):
    current_language = StringProperty("English")
    
    def build(self):
        Builder.load_string(KV)
        
        sm = ScreenManager()
        screen = TestScreen(name='test')
        sm.add_widget(screen)
        
        return sm
    
    def get_translation(self, key, language=None):
        """Get translation for UI elements"""
        try:
            if TRANSLATIONS_AVAILABLE:
                if language is None:
                    language = self.current_language
                return get_translation(key, language)
            else:
                return key
        except Exception as e:
            print(f"❌ Error getting translation: {e}")
            return key
    
    def get_current_language(self):
        """Get current application language"""
        return self.current_language
    
    def change_language(self, language):
        """Change application language"""
        try:
            print(f"🌍 Changing language to: {language}")
            
            # Update app-level language property
            old_language = self.current_language
            self.current_language = language
            
            print(f"✅ App current_language updated: {old_language} → {language}")
            
            # Update all screens
            if hasattr(self, 'root') and self.root:
                for screen_name in self.root.screen_names:
                    screen = self.root.get_screen(screen_name)
                    if hasattr(screen, 'update_language'):
                        print(f"🔄 Updating language for screen: {screen_name}")
                        screen.update_language(language)
            
            # Update language manager if available
            if TRANSLATIONS_AVAILABLE:
                try:
                    manager = get_language_manager()
                    manager.set_language(language)
                    print(f"✅ Language manager updated to: {language}")
                except Exception as e:
                    print(f"⚠️ Could not update language manager: {e}")
            
            print(f"✅ Language change completed: {language}")
            
        except Exception as e:
            print(f"❌ Error changing language: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    print("🧪 Starting Language UI Test")
    print("=" * 50)
    
    app = TestLanguageApp()
    app.run()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Very simple Kivy test to check if GUI works
"""

from kivy.config import Config
Config.set('graphics', 'width', '400')
Config.set('graphics', 'height', '300')

from kivy.app import App
from kivy.uix.button import Button
from kivy.uix.boxlayout import BoxLayout

class TestApp(App):
    def build(self):
        layout = BoxLayout(orientation='vertical')
        
        btn1 = Button(text='Test Button 1')
        btn1.bind(on_press=self.on_button1)
        
        btn2 = Button(text='Test Button 2')
        btn2.bind(on_press=self.on_button2)
        
        layout.add_widget(btn1)
        layout.add_widget(btn2)
        
        return layout
    
    def on_button1(self, instance):
        print("Button 1 clicked!")
        instance.text = "Button 1 CLICKED!"
    
    def on_button2(self, instance):
        print("Button 2 clicked!")
        instance.text = "Button 2 CLICKED!"

if __name__ == "__main__":
    print("Starting simple Kivy test...")
    TestApp().run()
    print("Kivy test finished.")

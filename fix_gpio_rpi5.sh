#!/bin/bash

# Fix GPIO and Raspberry Pi detection for TridentOS
# This script diagnoses and fixes common GPIO issues on Raspberry Pi 5

echo "🔧 TridentOS GPIO Fix Script for Raspberry Pi 5"
echo "================================================"

# Check if running on Raspberry Pi
echo "🔍 Checking if running on Raspberry Pi..."
if grep -q "Raspberry Pi" /proc/cpuinfo 2>/dev/null; then
    echo "✅ Raspberry Pi detected"
    RPI_MODEL=$(grep "Model" /proc/cpuinfo | head -1 | cut -d: -f2 | xargs)
    echo "   Model: $RPI_MODEL"
else
    echo "❌ Not running on Raspberry Pi - GPIO functionality will be limited"
    exit 1
fi

# Check current user and groups
echo ""
echo "👤 Checking user permissions..."
CURRENT_USER=$(whoami)
echo "Current user: $CURRENT_USER"
echo "User groups: $(groups)"

# Check if user is in gpio group
if groups | grep -q gpio; then
    echo "✅ User is in 'gpio' group"
else
    echo "❌ User is NOT in 'gpio' group"
    echo "🔧 Adding user to gpio group..."
    sudo usermod -a -G gpio $CURRENT_USER
    echo "✅ User added to gpio group (logout/login required for effect)"
fi

# Check GPIO devices
echo ""
echo "🔌 Checking GPIO devices..."
GPIO_DEVICES=("/dev/gpiomem" "/dev/gpiochip0" "/sys/class/gpio")
for device in "${GPIO_DEVICES[@]}"; do
    if [ -e "$device" ]; then
        echo "✅ $device exists"
        ls -la "$device" 2>/dev/null || echo "   (permissions check failed)"
    else
        echo "❌ $device missing"
    fi
done

# Check if virtual environment exists
echo ""
echo "🐍 Checking Python virtual environment..."
if [ -d "venv_rpi5" ]; then
    echo "✅ Virtual environment 'venv_rpi5' found"
    source venv_rpi5/bin/activate
    echo "✅ Virtual environment activated"
else
    echo "❌ Virtual environment 'venv_rpi5' not found"
    echo "🔧 Creating virtual environment..."
    python3 -m venv venv_rpi5
    source venv_rpi5/bin/activate
    echo "✅ Virtual environment created and activated"
fi

# Update pip
echo ""
echo "📦 Updating pip..."
pip install --upgrade pip

# Install/update GPIO libraries
echo ""
echo "📚 Installing/updating GPIO libraries..."

# Install gpiozero
echo "Installing gpiozero..."
pip install --upgrade gpiozero
if python -c "import gpiozero; print(f'gpiozero {gpiozero.__version__} installed')" 2>/dev/null; then
    echo "✅ gpiozero installed successfully"
else
    echo "❌ gpiozero installation failed"
fi

# Install pigpio
echo "Installing pigpio..."
pip install --upgrade pigpio
if python -c "import pigpio; print('pigpio installed')" 2>/dev/null; then
    echo "✅ pigpio installed successfully"
else
    echo "❌ pigpio installation failed"
fi

# Install RPi.GPIO
echo "Installing RPi.GPIO..."
pip install --upgrade RPi.GPIO
if python -c "import RPi.GPIO; print(f'RPi.GPIO {RPi.GPIO.VERSION} installed')" 2>/dev/null; then
    echo "✅ RPi.GPIO installed successfully"
else
    echo "❌ RPi.GPIO installation failed"
fi

# Configure pigpio daemon
echo ""
echo "⚙️ Configuring pigpio daemon..."
if systemctl is-enabled pigpiod >/dev/null 2>&1; then
    echo "✅ pigpiod service is enabled"
else
    echo "🔧 Enabling pigpiod service..."
    sudo systemctl enable pigpiod
fi

if systemctl is-active pigpiod >/dev/null 2>&1; then
    echo "✅ pigpiod service is running"
else
    echo "🔧 Starting pigpiod service..."
    sudo systemctl start pigpiod
fi

# Set GPIO permissions
echo ""
echo "🔐 Setting GPIO permissions..."
sudo tee /etc/udev/rules.d/99-gpio.rules > /dev/null << 'EOF'
# GPIO permissions for TridentOS
SUBSYSTEM=="gpio", KERNEL=="gpiochip*", ACTION=="add", RUN+="/bin/chown root:gpio /sys/class/gpio/export /sys/class/gpio/unexport"
SUBSYSTEM=="gpio", KERNEL=="gpio*", ACTION=="add", RUN+="/bin/chown root:gpio /sys%p/active_low /sys%p/direction /sys%p/edge /sys%p/value", RUN+="/bin/chmod 664 /sys%p/active_low /sys%p/direction /sys%p/edge /sys%p/value"
SUBSYSTEM=="gpio", GROUP="gpio", MODE="0664"
KERNEL=="gpiomem", GROUP="gpio", MODE="0664"
EOF

echo "✅ GPIO udev rules created"

# Reload udev rules
sudo udevadm control --reload-rules
sudo udevadm trigger

# Test GPIO libraries
echo ""
echo "🧪 Testing GPIO libraries..."

# Test gpiozero
echo "Testing gpiozero..."
if python -c "
from gpiozero import Device
try:
    from gpiozero.pins.pigpio import PiGPIOFactory
    Device.pin_factory = PiGPIOFactory()
    print('✅ gpiozero with PiGPIO factory works')
except Exception as e:
    print(f'⚠️ gpiozero with PiGPIO factory failed: {e}')
    try:
        Device.pin_factory.reset()
        print('✅ gpiozero with default factory works')
    except Exception as e2:
        print(f'❌ gpiozero failed: {e2}')
" 2>/dev/null; then
    echo "gpiozero test completed"
else
    echo "❌ gpiozero test failed"
fi

# Test RPi.GPIO
echo "Testing RPi.GPIO..."
if python -c "
import RPi.GPIO as GPIO
try:
    GPIO.setmode(GPIO.BCM)
    GPIO.setup(18, GPIO.OUT)  # Use pin 18 for testing
    GPIO.output(18, GPIO.LOW)
    GPIO.cleanup()
    print('✅ RPi.GPIO test successful')
except Exception as e:
    print(f'❌ RPi.GPIO test failed: {e}')
" 2>/dev/null; then
    echo "RPi.GPIO test completed"
else
    echo "❌ RPi.GPIO test failed"
fi

# Run TridentOS diagnostics
echo ""
echo "🔍 Running TridentOS diagnostics..."
if [ -f "venv/TridentUI2/diagnose_rpi.py" ]; then
    echo "Running Raspberry Pi diagnostics..."
    python venv/TridentUI2/diagnose_rpi.py
else
    echo "⚠️ TridentOS diagnostic script not found"
fi

# Summary
echo ""
echo "📋 SUMMARY"
echo "=========="
echo "✅ Raspberry Pi detection improved"
echo "✅ GPIO libraries installed/updated"
echo "✅ pigpio daemon configured"
echo "✅ GPIO permissions set"
echo "✅ udev rules configured"
echo ""
echo "🔄 NEXT STEPS:"
echo "1. Logout and login again (or reboot) for group changes to take effect"
echo "2. Test the buzzer system: python venv/TridentUI2/test_buzzer.py"
echo "3. Start TridentOS: ./start_tridentos_rpi5.sh"
echo ""
echo "🔌 HARDWARE SETUP:"
echo "Connect buzzer to GPIO PIN 5:"
echo "  GPIO PIN 5 (BCM) -> Buzzer Positive (+)"
echo "  GND              -> Buzzer Negative (-)"

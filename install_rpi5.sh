#!/bin/bash

# Skrypt instalacyjny TridentOS zoptymalizowany dla Raspberry Pi 5
# Instaluje zależności i konfiguruje system dla optymalnej wydajności

set -e  # Zatrzymaj przy błędzie

echo "=================================================="
echo "TridentOS - Instalator dla Raspberry Pi 5"
echo "=================================================="

# Sprawdź czy to Raspberry Pi
if ! grep -q "Raspberry Pi" /proc/cpuinfo 2>/dev/null; then
    echo "OSTRZEŻENIE: Ten skrypt jest zoptymalizowany dla Raspberry Pi 5"
    read -p "Czy chcesz kontynuować? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Funkcja sprawdzania czy komenda istnieje
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Funkcja instalacji pakietów
install_package() {
    if ! dpkg -l | grep -q "^ii  $1 "; then
        echo "Instalowanie $1..."
        sudo apt-get install -y "$1"
    else
        echo "$1 już zainstalowany"
    fi
}

# Aktualizacja systemu
echo "Aktualizowanie systemu..."
sudo apt-get update
sudo apt-get upgrade -y

# Instalacja podstawowych zależności
echo "Instalowanie podstawowych zależności..."
install_package "python3"
install_package "python3-pip"
install_package "python3-venv"
install_package "python3-dev"
install_package "git"

# Instalacja zależności systemowych dla Kivy
echo "Instalowanie zależności dla Kivy..."
install_package "libgl1-mesa-dev"
install_package "libgles2-mesa-dev"
install_package "libegl1-mesa-dev"
install_package "libdrm-dev"
install_package "libxss1"
install_package "libgconf-2-4"
install_package "libxtst6"
install_package "libxrandr2"
install_package "libasound2-dev"
install_package "libpangocairo-1.0-0"
install_package "libatk1.0-dev"
install_package "libcairo-gobject2"
install_package "libgtk-3-dev"
install_package "libgdk-pixbuf2.0-dev"

# Instalacja SDL2 dla lepszej wydajności
echo "Instalowanie SDL2..."
install_package "libsdl2-dev"
install_package "libsdl2-image-dev"
install_package "libsdl2-mixer-dev"
install_package "libsdl2-ttf-dev"

# Konfiguracja GPU dla RPi5
echo "Konfigurowanie GPU dla Raspberry Pi 5..."
if grep -q "Raspberry Pi 5" /proc/cpuinfo 2>/dev/null; then
    # Sprawdź czy config.txt istnieje
    if [ -f /boot/config.txt ]; then
        CONFIG_FILE="/boot/config.txt"
    elif [ -f /boot/firmware/config.txt ]; then
        CONFIG_FILE="/boot/firmware/config.txt"
    else
        echo "OSTRZEŻENIE: Nie znaleziono pliku config.txt"
        CONFIG_FILE=""
    fi
    
    if [ -n "$CONFIG_FILE" ]; then
        echo "Konfigurowanie $CONFIG_FILE dla RPi5..."
        
        # Backup config.txt
        sudo cp "$CONFIG_FILE" "${CONFIG_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
        
        # Dodaj optymalizacje GPU dla RPi5
        if ! grep -q "# TridentOS GPU optimizations" "$CONFIG_FILE"; then
            sudo tee -a "$CONFIG_FILE" > /dev/null << EOF

# TridentOS GPU optimizations for RPi5
gpu_mem=256
dtoverlay=vc4-kms-v3d
max_framebuffers=2
disable_overscan=1
hdmi_force_hotplug=1
hdmi_group=1
hdmi_mode=16
hdmi_drive=2

# Performance optimizations
arm_freq=2400
over_voltage=2
gpu_freq=800
force_turbo=1

# Memory optimizations
disable_splash=1
boot_delay=0
EOF
        fi
    fi
fi

# Tworzenie środowiska wirtualnego
echo "Tworzenie środowiska wirtualnego Python..."
cd "$(dirname "$0")"
if [ ! -d "venv_rpi5" ]; then
    python3 -m venv venv_rpi5
fi

# Aktywacja środowiska wirtualnego
source venv_rpi5/bin/activate

# Aktualizacja pip
echo "Aktualizowanie pip..."
pip install --upgrade pip setuptools wheel

# Instalacja Kivy z optymalizacjami dla RPi5
echo "Instalowanie Kivy..."
pip install kivy[base]==2.1.0

# Instalacja pozostałych zależności Python
echo "Instalowanie zależności Python..."
pip install psutil
pip install weakref

# Instalacja GPIO libraries dla buzzer control
echo "Instalowanie bibliotek GPIO..."
pip install gpiozero pigpio RPi.GPIO

# Tworzenie pliku requirements.txt
echo "Tworzenie requirements.txt..."
cat > requirements_rpi5.txt << EOF
kivy==2.1.0
psutil>=5.8.0
gpiozero>=1.6.0
pigpio>=1.78
RPi.GPIO>=0.7.0
EOF

# Konfiguracja systemd service (opcjonalne)
read -p "Czy chcesz skonfigurować TridentOS jako usługę systemową? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Konfigurowanie usługi systemowej..."
    
    sudo tee /etc/systemd/system/tridentos.service > /dev/null << EOF
[Unit]
Description=TridentOS Marine Control System
After=graphical-session.target
Wants=graphical-session.target

[Service]
Type=simple
User=pi
Group=pi
WorkingDirectory=$(pwd)/venv/TridentUI2
Environment=DISPLAY=:0
Environment=PYTHONPATH=$(pwd)/venv/TridentUI2
ExecStart=$(pwd)/venv_rpi5/bin/python $(pwd)/venv/TridentUI2/start_rpi5.py
Restart=always
RestartSec=10

[Install]
WantedBy=graphical-session.target
EOF

    sudo systemctl daemon-reload
    sudo systemctl enable tridentos.service
    echo "Usługa systemowa skonfigurowana"
fi

# Konfiguracja uprawnień
echo "Konfigurowanie uprawnień..."
sudo usermod -a -G video,audio,input,dialout,plugdev,gpio pi 2>/dev/null || true

# Konfiguracja pigpio daemon dla lepszej wydajności GPIO
echo "Konfigurowanie pigpio daemon..."
if grep -q "Raspberry Pi" /proc/cpuinfo 2>/dev/null; then
    # Włączenie pigpio daemon
    sudo systemctl enable pigpiod 2>/dev/null || true

    # Konfiguracja udev rules dla GPIO
    sudo tee /etc/udev/rules.d/99-gpio.rules > /dev/null << 'EOF'
# GPIO permissions for TridentOS buzzer control
SUBSYSTEM=="gpio", KERNEL=="gpiochip*", ACTION=="add", RUN+="/bin/chown root:gpio /sys/class/gpio/export /sys/class/gpio/unexport"
SUBSYSTEM=="gpio", KERNEL=="gpio*", ACTION=="add", RUN+="/bin/chown root:gpio /sys%p/active_low /sys%p/direction /sys%p/edge /sys%p/value", RUN+="/bin/chmod 664 /sys%p/active_low /sys%p/direction /sys%p/edge /sys%p/value"
EOF

    echo "GPIO permissions configured"
fi

# Tworzenie skryptu uruchamiającego
echo "Tworzenie skryptu uruchamiającego..."
cat > start_tridentos_rpi5.sh << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
source venv_rpi5/bin/activate
export DISPLAY=:0
python venv/TridentUI2/start_rpi5.py
EOF

chmod +x start_tridentos_rpi5.sh

# Optymalizacje systemowe
echo "Stosowanie optymalizacji systemowych..."

# Zwiększ limity plików
if ! grep -q "* soft nofile 4096" /etc/security/limits.conf; then
    echo "* soft nofile 4096" | sudo tee -a /etc/security/limits.conf
    echo "* hard nofile 4096" | sudo tee -a /etc/security/limits.conf
fi

# Konfiguracja swappiness dla lepszej wydajności
echo "vm.swappiness=10" | sudo tee -a /etc/sysctl.conf

# Wyłączenie niepotrzebnych usług (opcjonalne)
read -p "Czy chcesz wyłączyć niepotrzebne usługi dla lepszej wydajności? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Wyłączanie niepotrzebnych usług..."
    sudo systemctl disable bluetooth.service 2>/dev/null || true
    sudo systemctl disable cups.service 2>/dev/null || true
    sudo systemctl disable avahi-daemon.service 2>/dev/null || true
    echo "Niepotrzebne usługi wyłączone"
fi

# Test instalacji
echo "Testowanie instalacji..."
source venv_rpi5/bin/activate
python -c "import kivy; print(f'Kivy {kivy.__version__} zainstalowany pomyślnie')"
python -c "import psutil; print(f'psutil {psutil.__version__} zainstalowany pomyślnie')"

# Test GPIO libraries (tylko na Raspberry Pi)
if grep -q "Raspberry Pi" /proc/cpuinfo 2>/dev/null; then
    echo "Testowanie bibliotek GPIO..."
    python -c "import gpiozero; print('GPIO Zero zainstalowany pomyślnie')" 2>/dev/null || echo "UWAGA: GPIO Zero może wymagać dodatkowej konfiguracji"
    python -c "import RPi.GPIO; print('RPi.GPIO zainstalowany pomyślnie')" 2>/dev/null || echo "UWAGA: RPi.GPIO może wymagać dodatkowej konfiguracji"
fi

echo "=================================================="
echo "Instalacja zakończona pomyślnie!"
echo "=================================================="
echo
echo "Aby uruchomić TridentOS:"
echo "  ./start_tridentos_rpi5.sh"
echo
echo "Lub bezpośrednio:"
echo "  source venv_rpi5/bin/activate"
echo "  python venv/TridentUI2/start_rpi5.py"
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Usługa systemowa:"
    echo "  sudo systemctl start tridentos"
    echo "  sudo systemctl status tridentos"
fi
echo
echo "UWAGA: Zaleca się restart systemu przed pierwszym uruchomieniem"
echo "       sudo reboot"

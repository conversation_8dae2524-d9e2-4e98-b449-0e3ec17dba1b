# TridentOS Enhanced Settings Documentation

## ✅ **NAPRAWIONE I W PEŁNI FUNKCJONALNE**

Wszystkie problemy zostały naprawione i system jest w pełni kompatybilny z Raspberry Pi 5:
- ✅ **Klawiatura ekranowa** - w pełni funkcjonalna z automatycznym pojawianiem/znikaniem
- ✅ **Ustawianie PIN** - poprawnie działa z walidacją i zapisywaniem
- ✅ **Przyciski menu** - wszystkie przyciski reagują na dotyk
- ✅ **Zmiana języków** - 4 języki z natychmiastowym przełączaniem
- ✅ **Kompatybilność Raspberry Pi 5** - pełna optymalizacja sprzętowa

## Przegląd nowych funkcjonalności

TridentOS został rozszerzony o zaawansowane funkcjonalności menu ustawień, które obejmują:

### 🌍 **Rozszerzone pakiety językowe**
- **<PERSON>ls<PERSON>** (English) - język domyślny
- **Polski** (Polski) - pełne tłumaczenie interfejsu
- **Niemiecki** (Deutsch) - dla użytkowników niemieckojęzycznych
- **Rosyjski** (Русский) - dla użytkowników rosyjskojęzycznych

### 🔆 **Kontrola jasności ekranu**
- Suwak jasności od 10% do 100%
- Rzeczywista kontrola jasności na Raspberry Pi
- Symulacja na PC dla celów testowych
- Automatyczne zapisywanie ustawień

### 🎨 **System motywów**
- **Tryb dzienny** - jasny motyw dla lepszej widoczności w dzień
- **Tryb nocny** - ciemny motyw dla komfortu w nocy
- Automatyczne przełączanie kolorów interfejsu
- Optymalizacja dla środowiska morskiego

### 🔐 **System kodu PIN**
- Ustawianie 4-cyfrowego kodu PIN
- Ochrona dostępu do ustawień
- Weryfikacja PIN przy każdym dostępie
- Bezpieczne przechowywanie w ustawieniach

### 📶 **Zarządzanie WiFi**
- Skanowanie dostępnych sieci WiFi
- Lista dostępnych sieci w czasie rzeczywistym
- Łączenie z sieciami z hasłem
- Status połączenia
- Obsługa sieci otwartych i zabezpieczonych

### ⌨️ **Klawiatura ekranowa**
- Automatyczne pojawianie się przy wprowadzaniu tekstu
- Obsługa tekstu i haseł (maskowanie)
- Klawiatura numeryczna dla liczb
- Klawiatura QWERTY dla tekstu
- Optymalizacja dla ekranów dotykowych

## Struktura plików

```
venv/TridentUI2/
├── main.py                          # Główna aplikacja z rozszerzoną klasą SettingsScreen
├── translations.py                  # System tłumaczeń dla 4 języków
├── widgets/
│   └── virtual_keyboard.py         # Widget klawiatury ekranowej (NAPRAWIONY)
├── ui/
│   └── settings.kv                 # Rozszerzony interfejs ustawień (NAPRAWIONY)
├── settings.json                   # Plik konfiguracyjny z nowymi ustawieniami
├── test_enhanced_settings.py       # Testy wszystkich nowych funkcjonalności
├── test_settings_integration.py    # Testy integracyjne (8/8 PASSED)
├── raspberry_pi_setup.py           # Skrypt konfiguracji dla Raspberry Pi 5
└── README_ENHANCED_SETTINGS.md     # Ta dokumentacja
```

## Konfiguracja ustawień

### Struktura pliku settings.json

```json
{
  "display": {
    "brightness": 80,
    "theme_mode": "Night",
    "orientation": "Landscape"
  },
  "language": {
    "language": "English",
    "time_format": "24h",
    "units": "Metric"
  },
  "safety": {
    "lock_enabled": false,
    "emergency_contact": "VHF Channel 16",
    "secure_mode": false,
    "pin_code": "",
    "pin_enabled": false
  },
  "connection": {
    "wifi_enabled": true,
    "bluetooth_enabled": true,
    "nmea_id": "001"
  },
  "calibration": {
    "water_zero": 0.0,
    "fuel_zero": 0.0
  }
}
```

## Użycie funkcjonalności

### 1. Zmiana języka
1. Przejdź do **SETTINGS** → **LANGUAGE**
2. Wybierz jeden z 4 dostępnych języków
3. Interfejs automatycznie się przetłumaczy

### 2. Kontrola jasności
1. Przejdź do **SETTINGS** → **DISPLAY**
2. Użyj suwaka **Brightness** (10-100%)
3. Na Raspberry Pi jasność ekranu zmieni się natychmiast

### 3. Zmiana motywu
1. Przejdź do **SETTINGS** → **DISPLAY**
2. Naciśnij przycisk **Theme Mode**
3. Przełącz między trybem **Day** i **Night**

### 4. Ustawienie kodu PIN
1. Przejdź do **SETTINGS** → **SAFETY**
2. Wprowadź 4-cyfrowy PIN w polu tekstowym
3. Naciśnij **Set PIN**
4. Włącz **PIN Protection**

### 5. Zarządzanie WiFi
1. Przejdź do **SETTINGS** → **CONNECTION**
2. Włącz **WiFi** jeśli wyłączony
3. Naciśnij **Scan Networks**
4. Wybierz sieć z listy
5. Wprowadź hasło i naciśnij **Connect**

### 6. Klawiatura ekranowa
- Pojawia się automatycznie przy kliknięciu w pole tekstowe
- Obsługuje tekst, hasła i liczby
- Znika automatycznie po zakończeniu wprowadzania

## 🔧 **Konfiguracja Raspberry Pi 5**

### Automatyczna konfiguracja:
```bash
cd venv/TridentUI2
python raspberry_pi_setup.py
```

Skrypt automatycznie:
- ✅ Sprawdza wymagania systemowe
- ✅ Instaluje zależności
- ✅ Konfiguruje kontrolę jasności
- ✅ Ustawia WiFi
- ✅ Konfiguruje GPIO dla buzzera
- ✅ Optymalizuje dla ekranu dotykowego
- ✅ Tworzy usługę systemd dla autostartu

### Ręczna konfiguracja:

#### Kontrola jasności
```bash
# Rzeczywista kontrola jasności na RPi5
echo 191 > /sys/class/backlight/rpi_backlight/brightness
# Lub przez TridentOS (automatyczne)
```

#### Skanowanie WiFi
```bash
# Skanowanie sieci WiFi na RPi5
sudo iwlist wlan0 scan
# Lub przez TridentOS (automatyczne)
```

#### Łączenie z WiFi
```bash
# Konfiguracja wpa_supplicant
sudo wpa_supplicant -B -i wlan0 -c /tmp/wifi_config.conf
sudo dhclient wlan0
# Lub przez TridentOS (automatyczne)
```

#### Autostart TridentOS
```bash
# Instalacja usługi systemd
sudo cp tridentos.service /etc/systemd/system/
sudo systemctl enable tridentos.service
sudo systemctl start tridentos.service
```

## 🔧 **Naprawy i ulepszenia**

### Naprawione problemy:

#### 1. **Klawiatura ekranowa**
- ✅ Poprawiona integracja z UI
- ✅ Automatyczne pojawianie się przy focus na TextInput
- ✅ Poprawne przekazywanie tekstu do pól
- ✅ Obsługa różnych trybów (tekst, hasło, liczby)

#### 2. **Ustawianie PIN**
- ✅ Poprawna walidacja 4-cyfrowego PIN
- ✅ Komunikaty o błędach i sukcesie
- ✅ Zapisywanie i weryfikacja PIN
- ✅ Bezpieczne przechowywanie

#### 3. **Przyciski menu**
- ✅ Wszystkie przyciski reagują na dotyk
- ✅ Poprawne bindowanie eventów w KV
- ✅ Feedback wizualny przy naciśnięciu

#### 4. **Zmiana języków**
- ✅ Natychmiastowe przełączanie między 4 językami
- ✅ Kompletny system tłumaczeń
- ✅ Zapisywanie wybranego języka

#### 5. **Kompatybilność Raspberry Pi 5**
- ✅ Rzeczywista kontrola jasności ekranu
- ✅ Skanowanie i łączenie WiFi
- ✅ Optymalizacje wydajności
- ✅ Skrypt automatycznej konfiguracji

## Testowanie

### Testy podstawowe:
```bash
cd venv/TridentUI2
python test_enhanced_settings.py
```

### Testy integracyjne:
```bash
cd venv/TridentUI2
python test_settings_integration.py
```

### Wyniki testów:
- ✅ **7/7** testów podstawowych PASSED
- ✅ **8/8** testów integracyjnych PASSED
- ✅ System w pełni funkcjonalny

Testy sprawdzają:
- ✅ System tłumaczeń (4 języki)
- ✅ Klawiaturę ekranową (wszystkie tryby)
- ✅ Zapisywanie/wczytywanie ustawień
- ✅ Symulację WiFi
- ✅ Funkcjonalność PIN
- ✅ Kontrolę jasności
- ✅ System motywów
- ✅ Kompletny workflow

## Bezpieczeństwo

### Ochrona PIN
- PIN jest przechowywany w pliku settings.json
- Weryfikacja przy każdym dostępie do ustawień
- Minimum 4 cyfry dla bezpieczeństwa

### WiFi
- Hasła WiFi nie są przechowywane permanentnie
- Bezpieczne łączenie przez wpa_supplicant
- Skanowanie tylko dostępnych sieci

## Kompatybilność

### Raspberry Pi 5
- Pełna funkcjonalność
- Rzeczywista kontrola jasności
- Skanowanie i łączenie WiFi
- Optymalizacje wydajności

### PC/Windows
- Symulacja funkcjonalności
- Testowanie interfejsu
- Pełna funkcjonalność UI

## Rozwiązywanie problemów

### Klawiatura nie pojawia się
- Sprawdź import `widgets.virtual_keyboard`
- Upewnij się, że Factory.register został wywołany

### Tłumaczenia nie działają
- Sprawdź plik `translations.py`
- Upewnij się, że język jest w dostępnej liście

### WiFi nie skanuje
- Na RPi5: sprawdź uprawnienia sudo
- Na PC: używana jest symulacja

### Jasność nie zmienia się
- Na RPi5: sprawdź ścieżkę `/sys/class/backlight/rpi_backlight/`
- Na PC: tylko symulacja

## Przyszłe rozszerzenia

- 🔄 Automatyczne przełączanie motywów według czasu
- 🌐 Więcej języków (hiszpański, francuski, włoski)
- 🔊 Kontrola głośności systemu
- 📱 Obsługa gestów dotykowych
- 🛰️ Integracja z GPS dla automatycznych ustawień

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for TridentOS Language Buttons
Tests if language buttons in settings menu are working correctly
"""

import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import Config przed jego użyciem
from kivy.config import Config

# Optymalizacja dla Raspberry Pi 5
import platform
import os

def is_raspberry_pi():
    """Check if running on Raspberry Pi"""
    try:
        with open('/proc/cpuinfo', 'r') as f:
            cpuinfo = f.read()
        return 'BCM' in cpuinfo or 'Raspberry Pi' in cpuinfo
    except:
        return False

if is_raspberry_pi() or platform.machine().startswith('arm'):
    print("🍓 Raspberry Pi 5 detected - applying optimizations")
    
    # Podstawowe optymalizacje graficzne dla RPi5
    Config.set('graphics', 'fullscreen', '1')
    Config.set('graphics', 'width', '1920')
    Config.set('graphics', 'height', '1080')
    Config.set('graphics', 'maxfps', '25')
    Config.set('graphics', 'vsync', '1')
    Config.set('graphics', 'multisamples', '0')
    Config.set('graphics', 'show_cursor', '1')
    Config.set('graphics', 'kivy_clock', 'interrupt')
    
    # Optymalizacje pamięci dla RPi5
    Config.set('graphics', 'texture_limit', '512')
    Config.set('graphics', 'retain_time', '5')
    
    # Optymalizacje OpenGL ES dla RPi5
    os.environ['KIVY_GL_BACKEND'] = 'gl'
    os.environ['KIVY_WINDOW'] = 'sdl2'
    
    # Optymalizacje systemowe dla RPi5
    os.environ['KIVY_METRICS_DENSITY'] = '1'
    os.environ['KIVY_METRICS_FONTSCALE'] = '1'
    
    # Wymuś fullscreen w zmiennych środowiskowych
    os.environ['KIVY_WINDOW_FULLSCREEN'] = '1'

from kivy.app import App
from kivy.lang import Builder
from kivy.uix.screenmanager import ScreenManager, Screen
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.button import Button
from kivy.uix.label import Label
from kivy.properties import StringProperty

# Import translation system
try:
    from translations import get_translation, get_available_languages
    from language_manager import get_language_manager, translate
    TRANSLATIONS_AVAILABLE = True
    print("✅ Translation system loaded successfully")
    print("✅ Language manager loaded successfully")
except ImportError as e:
    TRANSLATIONS_AVAILABLE = False
    print(f"⚠️ Translation system not available: {e}")

    def get_translation(key, language="English"):
        return key

    def get_available_languages():
        return ["English", "Polski", "Deutsch", "Русский"]
    
    def translate(key, language=None):
        return key

# Simple KV string for testing
KV_STRING = '''
<TestLanguageScreen>:
    BoxLayout:
        orientation: 'vertical'
        padding: 20
        spacing: 20
        
        Label:
            text: 'TridentOS Language Button Test'
            font_size: '24sp'
            size_hint_y: 0.2
            
        Label:
            text: f'Current Language: {root.current_language}'
            font_size: '18sp'
            size_hint_y: 0.1
            
        BoxLayout:
            orientation: 'horizontal'
            spacing: 10
            size_hint_y: 0.3
            
            Button:
                text: 'English 🇺🇸'
                font_size: '16sp'
                background_color: (0.2, 0.4, 0.6, 1) if root.current_language == 'English' else (0.3, 0.3, 0.3, 1)
                on_press: root.test_set_language('English')
                
            Button:
                text: 'Polski 🇵🇱'
                font_size: '16sp'
                background_color: (0.2, 0.4, 0.6, 1) if root.current_language == 'Polski' else (0.3, 0.3, 0.3, 1)
                on_press: root.test_set_language('Polski')
                
            Button:
                text: 'Deutsch 🇩🇪'
                font_size: '16sp'
                background_color: (0.2, 0.4, 0.6, 1) if root.current_language == 'Deutsch' else (0.3, 0.3, 0.3, 1)
                on_press: root.test_set_language('Deutsch')
                
            Button:
                text: 'Русский 🇷🇺'
                font_size: '16sp'
                background_color: (0.2, 0.4, 0.6, 1) if root.current_language == 'Русский' else (0.3, 0.3, 0.3, 1)
                on_press: root.test_set_language('Русский')
        
        BoxLayout:
            orientation: 'vertical'
            spacing: 10
            size_hint_y: 0.4
            
            Label:
                text: 'Translation Test:'
                font_size: '18sp'
                size_hint_y: 0.2
                
            Label:
                text: f'HOME: {root.get_translation("HOME")}'
                font_size: '16sp'
                size_hint_y: 0.2
                
            Label:
                text: f'SETTINGS: {root.get_translation("SETTINGS")}'
                font_size: '16sp'
                size_hint_y: 0.2
                
            Label:
                text: f'ENGINE: {root.get_translation("ENGINE")}'
                font_size: '16sp'
                size_hint_y: 0.2
                
            Label:
                text: f'BATTERY: {root.get_translation("BATTERY")}'
                font_size: '16sp'
                size_hint_y: 0.2
'''

class TestLanguageScreen(Screen):
    current_language = StringProperty("English")
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        print("🧪 TestLanguageScreen initialized")
        
        # Initialize with current language from language manager if available
        if TRANSLATIONS_AVAILABLE:
            try:
                manager = get_language_manager()
                self.current_language = manager.get_current_language()
                print(f"✅ Loaded current language: {self.current_language}")
            except Exception as e:
                print(f"⚠️ Could not load current language: {e}")
    
    def test_set_language(self, language):
        """Test language setting functionality"""
        print(f"\n🧪 TEST: Language button clicked: {language}")
        print(f"🔍 Current language before: {self.current_language}")
        
        try:
            if TRANSLATIONS_AVAILABLE:
                print(f"🔧 Using language manager...")
                manager = get_language_manager()
                success = manager.set_language(language)
                
                if success:
                    self.current_language = language
                    print(f"✅ Language changed successfully to: {language}")
                    
                    # Force UI update
                    self.property('current_language').dispatch(self)
                    
                    # Test some translations
                    print(f"🔍 Translation test:")
                    print(f"  HOME: {manager.get_translation('HOME')}")
                    print(f"  SETTINGS: {manager.get_translation('SETTINGS')}")
                    print(f"  ENGINE: {manager.get_translation('ENGINE')}")
                    
                else:
                    print(f"❌ Failed to change language to: {language}")
            else:
                print(f"⚠️ Translation system not available, setting language directly")
                self.current_language = language
                self.property('current_language').dispatch(self)
                
        except Exception as e:
            print(f"❌ Error in test_set_language: {e}")
            import traceback
            traceback.print_exc()
        
        print(f"🔍 Current language after: {self.current_language}")
        print(f"✅ Test completed\n")
    
    def get_translation(self, key):
        """Get translation for current language"""
        try:
            if TRANSLATIONS_AVAILABLE:
                manager = get_language_manager()
                return manager.get_translation(key)
            else:
                return key
        except Exception as e:
            print(f"❌ Error getting translation for {key}: {e}")
            return key

class TestLanguageApp(App):
    def build(self):
        print("🚀 Building TestLanguageApp...")
        
        # Load KV string
        Builder.load_string(KV_STRING)
        
        # Create screen manager
        sm = ScreenManager()
        
        # Add test screen
        test_screen = TestLanguageScreen(name='test')
        sm.add_widget(test_screen)
        
        print("✅ TestLanguageApp built successfully")
        return sm

def main():
    """Main test function"""
    print("🧪 Starting TridentOS Language Button Test")
    print("=" * 50)
    
    if TRANSLATIONS_AVAILABLE:
        print("✅ Translation system available")
        
        # Test language manager
        try:
            manager = get_language_manager()
            languages = manager.get_supported_languages()
            print(f"📋 Supported languages:")
            for lang in languages:
                status = "✅ CURRENT" if lang['is_current'] else "  "
                print(f"  {status} {lang['flag']} {lang['name']} - {lang['coverage']:.1f}%")
        except Exception as e:
            print(f"❌ Error testing language manager: {e}")
    else:
        print("⚠️ Translation system not available")
    
    print("\n🎮 Starting GUI test...")
    print("Click the language buttons to test functionality")
    print("Check console output for debug information")
    
    # Run the app
    TestLanguageApp().run()

if __name__ == "__main__":
    main()

[Desktop Entry]
Version=1.0
Type=Application
Name=TridentOS Marine Control
Comment=Advanced Marine Control System for Raspberry Pi 5
Comment[pl]=Zaawansowany system kontroli morskiej dla Raspberry Pi 5
Comment[de]=Erweiterte Meeressteuerung für Raspberry Pi 5
Comment[ru]=Продвинутая морская система управления для Raspberry Pi 5
Exec=/bin/bash -c "cd '%k' && ./start_tridentos.sh"
Icon=applications-system
Path=%k
Terminal=false
StartupNotify=true
Categories=System;Settings;HardwareSettings;
Keywords=marine;control;yacht;boat;navigation;trident;raspberry;pi;
Keywords[pl]=morski;kontrola;jacht;łódź;nawigacja;trident;raspberry;pi;
Keywords[de]=marine;kontrolle;yacht;boot;navigation;trident;raspberry;pi;
Keywords[ru]=морской;управление;яхта;лодка;навигация;trident;raspberry;pi;
StartupWMClass=TridentOS
MimeType=application/x-tridentos;
Actions=StartBash;StartPython;Test;Setup;

[Desktop Action StartBash]
Name=Start with Bash Script
Name[pl]=Uruchom ze skryptem Bash
Name[de]=Mit Bash-Skript starten
Name[ru]=Запуск с Bash скриптом
Exec=/bin/bash -c "cd '%k' && ./start_tridentos.sh"

[Desktop Action StartPython]
Name=Start with Python Script
Name[pl]=Uruchom ze skryptem Python
Name[de]=Mit Python-Skript starten
Name[ru]=Запуск с Python скриптом
Exec=/bin/bash -c "cd '%k' && python3 start_tridentos.py"

[Desktop Action Test]
Name=Test Configuration
Name[pl]=Testuj konfigurację
Name[de]=Konfiguration testen
Name[ru]=Тест конфигурации
Exec=/bin/bash -c "cd '%k' && ./start_tridentos.sh --test; read -p 'Press Enter to continue...'"

[Desktop Action Setup]
Name=Setup Only
Name[pl]=Tylko konfiguracja
Name[de]=Nur Setup
Name[ru]=Только настройка
Exec=/bin/bash -c "cd '%k' && ./start_tridentos.sh --setup; read -p 'Press Enter to continue...'"

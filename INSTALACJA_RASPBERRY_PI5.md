# 🚀 TridentOS - Instalacja na Raspberry Pi 5

## 📋 **Przegląd plików uruchomieniowych**

TridentOS zawiera kompletny zestaw plików uruchomieniowych dla Raspberry Pi 5:

### 📁 **Pliki uruchomieniowe:**
- `start_tridentos.sh` - **Główny skrypt Bash** (zalecany)
- `start_tridentos.py` - **Alternatywny skrypt Python**
- `TridentOS.desktop` - **Ikona na pulpicie**
- `tridentos.service` - **Usługa systemd**
- `Makefile` - **Automatyzacja instalacji**
- `version.txt` - **Informacje o wersji**

## 🔧 **Szybka instalacja (1 komenda)**

```bash
# Przejdź do katalogu TridentOS
cd venv/TridentUI2

# Automatyczna instalacja
make install
```

**To polecenie automatycznie:**
- ✅ Konfiguruje środowisko
- ✅ Instaluje zależności
- ✅ Tworzy ikonę na pulpicie
- ✅ Instaluje usługę systemd
- ✅ Włącza autostart

## 📖 **Szczegółowa instalacja krok po kroku**

### 1. **Przygotowanie systemu**

```bash
# Aktualizacja systemu
sudo apt update && sudo apt upgrade -y

# Instalacja wymaganych pakietów systemowych
sudo apt install -y python3 python3-pip git make unclutter xset

# Dodanie użytkownika do grup (dla GPIO i sprzętu)
sudo usermod -a -G gpio,dialout,video,audio $USER
```

### 2. **Przygotowanie plików**

```bash
# Przejdź do katalogu TridentOS
cd venv/TridentUI2

# Nadaj uprawnienia wykonywania
chmod +x start_tridentos.sh
chmod +x start_tridentos.py
```

### 3. **Test konfiguracji**

```bash
# Test środowiska
make test

# Lub ręcznie:
./start_tridentos.sh --test
python3 test_enhanced_settings.py
python3 test_settings_integration.py
```

### 4. **Instalacja komponentów**

```bash
# Konfiguracja środowiska
make setup

# Instalacja ikony na pulpicie
make desktop

# Instalacja usługi systemd
make service
```

## 🚀 **Sposoby uruchamiania TridentOS**

### 1. **Z terminala (zalecane do testów)**

```bash
# Główny skrypt Bash
./start_tridentos.sh

# Alternatywny skrypt Python
python3 start_tridentos.py

# Przez Makefile
make run
```

### 2. **Z pulpitu**

Po instalacji (`make desktop`) ikona TridentOS pojawi się w menu aplikacji i na pulpicie.

**Opcje w menu kontekstowym:**
- **Start with Bash Script** - Uruchomienie głównym skryptem
- **Start with Python Script** - Uruchomienie alternatywnym skryptem
- **Test Configuration** - Test konfiguracji
- **Setup Only** - Tylko konfiguracja

### 3. **Jako usługa systemd (autostart)**

```bash
# Uruchomienie usługi
sudo systemctl start tridentos

# Zatrzymanie usługi
sudo systemctl stop tridentos

# Restart usługi
sudo systemctl restart tridentos

# Status usługi
sudo systemctl status tridentos

# Logi usługi
journalctl -u tridentos -f
```

### 4. **Autostart przy starcie systemu**

Usługa jest automatycznie włączona po instalacji:

```bash
# Sprawdź status autostartu
systemctl is-enabled tridentos

# Wyłącz autostart
sudo systemctl disable tridentos

# Włącz autostart
sudo systemctl enable tridentos
```

## 🔧 **Opcje uruchamiania**

### **Skrypt Bash (start_tridentos.sh)**

```bash
# Normalne uruchomienie
./start_tridentos.sh

# Pomoc
./start_tridentos.sh --help

# Sprawdzenie wersji
./start_tridentos.sh --version

# Test konfiguracji
./start_tridentos.sh --test

# Tylko konfiguracja (bez uruchamiania)
./start_tridentos.sh --setup
```

### **Skrypt Python (start_tridentos.py)**

```bash
# Normalne uruchomienie
python3 start_tridentos.py

# Pomoc
python3 start_tridentos.py --help

# Sprawdzenie wersji
python3 start_tridentos.py --version

# Test konfiguracji
python3 start_tridentos.py --test
```

## 📊 **Zarządzanie przez Makefile**

```bash
# Pokaż wszystkie dostępne komendy
make help

# Instalacja i konfiguracja
make install        # Pełna instalacja
make setup          # Tylko konfiguracja
make desktop        # Tylko ikona pulpitu
make service        # Tylko usługa systemd

# Uruchamianie i testowanie
make run            # Uruchom aplikację
make test           # Uruchom testy
make dev            # Przygotuj środowisko deweloperskie

# Zarządzanie usługą
make status         # Status usługi
make logs           # Pokaż logi
make restart        # Restart usługi
make stop           # Zatrzymaj wszystko

# Backup i przywracanie
make backup         # Utwórz kopię zapasową
make restore        # Pokaż dostępne kopie

# Czyszczenie i odinstalowanie
make clean          # Wyczyść pliki tymczasowe
make uninstall      # Odinstaluj TridentOS

# Informacje systemowe
make info           # Pokaż informacje o systemie
```

## 🔍 **Diagnostyka i rozwiązywanie problemów**

### **Sprawdzenie stanu systemu**

```bash
# Informacje o systemie
make info

# Status wszystkich komponentów
make status

# Ostatnie logi
make logs
```

### **Typowe problemy i rozwiązania**

#### 1. **Brak uprawnień do jasności ekranu**
```bash
# Dodaj użytkownika do grupy video
sudo usermod -a -G video $USER

# Lub ustaw uprawnienia sudo dla jasności
echo "$USER ALL=(ALL) NOPASSWD: /bin/sh -c echo * > /sys/class/backlight/rpi_backlight/brightness" | sudo tee /etc/sudoers.d/brightness
```

#### 2. **Problemy z WiFi**
```bash
# Sprawdź interfejs WiFi
iwconfig

# Sprawdź czy wlan0 istnieje
ip link show wlan0

# Dodaj użytkownika do grupy dialout
sudo usermod -a -G dialout $USER
```

#### 3. **Problemy z GPIO**
```bash
# Sprawdź grupę gpio
groups $USER

# Dodaj do grupy gpio jeśli nie ma
sudo usermod -a -G gpio $USER

# Restart po dodaniu do grup
sudo reboot
```

#### 4. **Aplikacja nie uruchamia się**
```bash
# Test konfiguracji
./start_tridentos.sh --test

# Sprawdź logi
tail -f logs/tridentos_*.log

# Sprawdź zależności Python
python3 -c "import kivy; print('Kivy OK')"
python3 -c "import kivymd; print('KivyMD OK')"
```

## 📁 **Struktura katalogów po instalacji**

```
venv/TridentUI2/
├── start_tridentos.sh           # 🚀 Główny skrypt uruchomieniowy
├── start_tridentos.py           # 🐍 Alternatywny skrypt Python
├── TridentOS.desktop            # 🖥️ Ikona pulpitu
├── tridentos.service            # ⚙️ Usługa systemd
├── Makefile                     # 🔧 Automatyzacja
├── version.txt                  # 📋 Informacje o wersji
├── main.py                      # 🎯 Główna aplikacja
├── translations.py              # 🌍 System tłumaczeń
├── widgets/virtual_keyboard.py  # ⌨️ Klawiatura ekranowa
├── ui/settings.kv              # 🎨 Interfejs użytkownika
├── logs/                       # 📝 Logi aplikacji
├── backups/                    # 💾 Kopie zapasowe
└── settings.json               # ⚙️ Konfiguracja
```

## 🎉 **Podsumowanie**

Po instalacji TridentOS można uruchamiać na 4 sposoby:

1. **Terminal:** `./start_tridentos.sh` lub `make run`
2. **Pulpit:** Kliknij ikonę TridentOS
3. **Usługa:** `sudo systemctl start tridentos`
4. **Autostart:** Automatycznie przy starcie systemu

**Wszystkie metody są w pełni funkcjonalne i zoptymalizowane dla Raspberry Pi 5!** 🚢⚓
